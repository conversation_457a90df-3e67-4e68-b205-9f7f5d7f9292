export interface TemplatePreview {
  id: string;
  name: string;
  description: string;
  preview: string; // Base64 image or URL
  settings: any;
  data?: any;
}

export interface TemplateCategory {
  [key: string]: TemplatePreview[];
}

export const TEMPLATE_LIBRARY: TemplateCategory = {
  header: [
    {
      id: "header1",
      name: "Header Gradient",
      description: "Header với nền gradient và logo",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMjAwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjQwIiByPSIxNSIgZmlsbD0iI0Y1RjVGNSIvPgo8dGV4dCB4PSI0NSIgeT0iMzUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzAwMCI+RXZvdGVjaCB4aW4gY2jDoG88L3RleHQ+Cjx0ZXh0IHg9IjQ1IiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNjY2Ij5OZ8OgeSBt4bubaSDEkcOzIGzDoG5oIPCfkYs8L3RleHQ+CjxyZWN0IHg9IjE2MCIgeT0iMjUiIHdpZHRoPSIxNSIgaGVpZ2h0PSIxNSIgZmlsbD0iIzMzMyIvPgo8cmVjdCB4PSIxODAiIHk9IjI1IiB3aWR0aD0iMTUiIGhlaWdodD0iMTUiIGZpbGw9IiMzMzMiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8wXzEiIHgxPSIwIiB5MT0iMCIgeDI9IjAiIHkyPSI4MCI+CjxzdG9wIHN0b3AtY29sb3I9IiNFQ0ZFRTciLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRkZGRkZGIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+",
      settings: {
        margin: "0px 0px 0px 0px",
        padding: "0px 8px 8px 8px",
        background: "linear-gradient(180deg, RGB(236, 254, 231) 0%, rgba(255,255,255,1) 100%)",
        border: "0px solid rgba(0, 0, 0, 0.1)",
        borderRadius: "0px 0px 0px 0px",
        visibleTitle: true,
        title: "Evotech xin chào",
        visibleSubTitle: true,
        subTitle: "Ngày mới tốt lành 👋",
        colorTitle: "rgba(0, 0, 0, 1)",
        fontSizeTitle: 14,
        colorSubTitle: "rgba(0, 0, 0, 1)",
        fontSizeSubTitle: 12,
        visibleCartIcon: true,
        visibleLogo: true,
        visibleMessageIcon: false,
        visibleSearchBar: false
      }
    },
    {
      id: "header2",
      name: "Header Simple",
      description: "Header đơn giản với nền trắng",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMjAwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiNFNUU3RUIiLz4KPGJ0ZXh0IHg9IjEwIiB5PSI0NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjMDAwIj5UaXRsZTwvdGV4dD4KPHJlY3QgeD0iMTYwIiB5PSIzMCIgd2lkdGg9IjE1IiBoZWlnaHQ9IjE1IiBmaWxsPSIjMzMzIi8+CjxyZWN0IHg9IjE4MCIgeT0iMzAiIHdpZHRoPSIxNSIgaGVpZ2h0PSIxNSIgZmlsbD0iIzMzMyIvPgo8L3N2Zz4=",
      settings: {
        margin: "0px 0px 0px 0px",
        padding: "8px 8px 8px 8px",
        background: "rgba(255,255,255,1)",
        border: "1px solid rgba(229, 231, 235, 1)",
        borderRadius: "0px 0px 0px 0px",
        visibleTitle: true,
        title: "Title",
        visibleSubTitle: false,
        subTitle: "",
        colorTitle: "rgba(0, 0, 0, 1)",
        fontSizeTitle: 16,
        visibleCartIcon: true,
        visibleLogo: false,
        visibleMessageIcon: true,
        visibleSearchBar: false
      }
    },
    {
      id: "header3",
      name: "Header với Search",
      description: "Header có thanh tìm kiếm",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDIwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjEwIiB5PSIyNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjMDAwIj5UaXRsZTwvdGV4dD4KPHJlY3QgeD0iMTYwIiB5PSIxMCIgd2lkdGg9IjE1IiBoZWlnaHQ9IjE1IiBmaWxsPSIjMzMzIi8+CjxyZWN0IHg9IjE4MCIgeT0iMTAiIHdpZHRoPSIxNSIgaGVpZ2h0PSIxNSIgZmlsbD0iIzMzMyIvPgo8cmVjdCB4PSIxMCIgeT0iNDAiIHdpZHRoPSIxODAiIGhlaWdodD0iMzAiIGZpbGw9IiNGOUZBRkIiIHN0cm9rZT0iI0Q5RDlEOSIgcng9IjQiLz4KPHR4dCB4PSIyMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OSI+VMOsbSBraeG6v20uLi48L3RleHQ+Cjwvc3ZnPg==",
      settings: {
        margin: "0px 0px 0px 0px",
        padding: "8px 8px 8px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0, 0, 0, 0.1)",
        borderRadius: "0px 0px 0px 0px",
        visibleTitle: true,
        title: "Title",
        visibleSubTitle: false,
        subTitle: "",
        colorTitle: "rgba(0, 0, 0, 1)",
        fontSizeTitle: 16,
        visibleCartIcon: true,
        visibleLogo: false,
        visibleMessageIcon: true,
        visibleSearchBar: true,
        placeholderSearchBar: "Tìm kiếm..."
      }
    }
  ],
  category: [
    {
      id: "category1",
      name: "Grid 2 cột",
      description: "Danh mục hiển thị 2 cột",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1IiByeD0iOCIvPgo8cmVjdCB4PSIxMTAiIHk9IjEwIiB3aWR0aD0iODAiIGhlaWdodD0iODAiIGZpbGw9IiNGNUY1RjUiIHJ4PSI4Ii8+Cjx0ZXh0IHg9IjUwIiB5PSIxMDUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RGFuaCBt4bulYzwvdGV4dD4KPHR4dCB4PSIxNTAiIHk9IjEwNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjMzMzIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5EYW5oIG3hu6VjPC90ZXh0Pgo8L3N2Zz4=",
      settings: {
        col: 2,
        margin: "0px 0px 0px 0px",
        padding: "8px 8px 8px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px",
        cardStyle: "default"
      }
    },
    {
      id: "category2",
      name: "Grid 3 cột",
      description: "Danh mục hiển thị 3 cột",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjVGNUY1IiByeD0iOCIvPgo8cmVjdCB4PSI3NSIgeT0iMTAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgZmlsbD0iI0Y1RjVGNSIgcng9IjgiLz4KPHJlY3QgeD0iMTQwIiB5PSIxMCIgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjVGNUY1IiByeD0iOCIvPgo8dGV4dCB4PSIzNSIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RGFuaCBt4bulYzwvdGV4dD4KPHR4dCB4PSIxMDAiIHk9IjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiMzMzMiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRhbmggbeG7pWM8L3RleHQ+Cjx0ZXh0IHg9IjE2NSIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RGFuaCBt4bulYzwvdGV4dD4KPC9zdmc+",
      settings: {
        col: 3,
        margin: "0px 0px 0px 0px",
        padding: "8px 8px 8px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px",
        cardStyle: "rounded"
      }
    },
    {
      id: "category3",
      name: "Grid 4 cột",
      description: "Danh mục hiển thị 4 cột",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjM1IiBoZWlnaHQ9IjM1IiBmaWxsPSIjRjVGNUY1IiByeD0iNCIvPgo8cmVjdCB4PSI1NSIgeT0iMTAiIHdpZHRoPSIzNSIgaGVpZ2h0PSIzNSIgZmlsbD0iI0Y1RjVGNSIgcng9IjQiLz4KPHJlY3QgeD0iMTAwIiB5PSIxMCIgd2lkdGg9IjM1IiBoZWlnaHQ9IjM1IiBmaWxsPSIjRjVGNUY1IiByeD0iNCIvPgo8cmVjdCB4PSIxNDUiIHk9IjEwIiB3aWR0aD0iMzUiIGhlaWdodD0iMzUiIGZpbGw9IiNGNUY1RjUiIHJ4PSI0Ii8+Cjx0ZXh0IHg9IjI3LjUiIHk9IjY1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iOCIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RGFuaCBt4bulYzwvdGV4dD4KPHR4dCB4PSI3Mi41IiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjgiIGZpbGw9IiMzMzMiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRhbmggbeG7pWM8L3RleHQ+Cjx0ZXh0IHg9IjExNy41IiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjgiIGZpbGw9IiMzMzMiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRhbmggbeG7pWM8L3RleHQ+Cjx0ZXh0IHg9IjE2Mi41IiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjgiIGZpbGw9IiMzMzMiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRhbmggbeG7pWM8L3RleHQ+Cjwvc3ZnPg==",
      settings: {
        col: 4,
        margin: "0px 0px 0px 0px",
        padding: "8px 8px 8px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px",
        cardStyle: "shadow"
      }
    }
  ],
  banner: [
    {
      id: "banner1",
      name: "Banner Full Width",
      description: "Banner chiều rộng đầy đủ",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDIwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSI4MCIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzBfMSkiIHJ4PSI4Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI0ZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+QmFubmVyPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTgwIiB5Mj0iODAiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkY2QjZCIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iI0ZGOTk5OSIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPg==",
      settings: {
        margin: "0px 0px 0px 0px",
        padding: "8px 0px 8px 0px",
        background: "rgb(255, 255, 255)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px",
        imageStyle: {
          height: 164,
          borderRadius: "8px 8px 8px 8px"
        },
        autoplay: 3000,
        animationPagination: true
      }
    },
    {
      id: "banner2",
      name: "Banner Compact",
      description: "Banner nhỏ gọn",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiB2aWV3Qm94PSIwIDAgMjAwIDgwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjEwIiB5PSIyMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSI0MCIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzBfMSkiIHJ4PSI0Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iNDUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iI0ZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+QmFubmVyPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzBfMSIgeDE9IjAiIHkxPSIwIiB4Mj0iMTgwIiB5Mj0iNDAiPgo8c3RvcCBzdG9wLWNvbG9yPSIjNjhCNTU0Ii8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzk5RDU4NCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPg==",
      settings: {
        margin: "0px 0px 0px 0px",
        padding: "8px 0px 8px 0px",
        background: "rgb(255, 255, 255)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px",
        imageStyle: {
          height: 44,
          borderRadius: "4px 4px 4px 4px"
        },
        autoplay: 7000,
        animationPagination: false
      }
    }
  ],
  flashsale: [
    {
      id: "flashsale1",
      name: "Flash Sale với Timer",
      description: "Flash sale có đồng hồ đếm ngược",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXJfMF8xKSIvPgo8dGV4dCB4PSIxMCIgeT0iMjUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzAwMCIgZm9udC13ZWlnaHQ9ImJvbGQiPkbimqHvuI9hc2ggU2FsZTwvdGV4dD4KPHR4dCB4PSIxNTAiIHk9IjI1IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiM2NjYiPkvhur90IHRow7pjIHRyb25nPC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iNDAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI3MCIgZmlsbD0iI0ZGRiIgcng9IjgiLz4KPHJlY3QgeD0iMTEwIiB5PSI0MCIgd2lkdGg9IjgwIiBoZWlnaHQ9IjcwIiBmaWxsPSIjRkZGIiByeD0iOCIvPgo8dGV4dCB4PSI1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiMzMzMiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlPhuqNuIHBo4bqpbTwvdGV4dD4KPHR4dCB4PSIxNTAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSIjMzMzIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5T4bqjbiBwaOG6qW08L3RleHQ+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXJfMF8xIiB4MT0iMCIgeTE9IjAiIHgyPSIyMDAiIHkyPSIxMjAiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkZFQkVCIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iI0ZGRjVGNSIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPg==",
      settings: {
        title: "F⚡️ash Sale",
        subTitle: "Kết thúc trong",
        visibleTimer: true,
        margin: "0px 0px 0px 0px",
        padding: "16px 8px 16px 8px",
        background: "linear-gradient(45deg, #FFEBEB 0%, #FFF5F5 100%)",
        border: "0px solid rgba(0, 0, 0, 0.1)",
        borderRadius: "0px 0px 0px 0px"
      }
    },
    {
      id: "flashsale2",
      name: "Flash Sale Simple",
      description: "Flash sale đơn giản không timer",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDIwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjEwIiB5PSIyNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSIjRUUyOTI5IiBmb250LXdlaWdodD0iYm9sZCI+Rmxhc2ggU2FsZTwvdGV4dD4KPHJlY3QgeD0iMTAiIHk9IjM1IiB3aWR0aD0iNjAiIGhlaWdodD0iNTUiIGZpbGw9IiNGRkYiIHN0cm9rZT0iI0U1RTdFQiIgcng9IjQiLz4KPHJlY3QgeD0iODAiIHk9IjM1IiB3aWR0aD0iNjAiIGhlaWdodD0iNTUiIGZpbGw9IiNGRkYiIHN0cm9rZT0iI0U1RTdFQiIgcng9IjQiLz4KPHJlY3QgeD0iMTUwIiB5PSIzNSIgd2lkdGg9IjQwIiBoZWlnaHQ9IjU1IiBmaWxsPSIjRkZGIiBzdHJva2U9IiNFNUU3RUIiIHJ4PSI0Ii8+Cjwvc3ZnPg==",
      settings: {
        title: "Flash Sale",
        subTitle: "",
        visibleTimer: false,
        margin: "0px 0px 0px 0px",
        padding: "16px 8px 16px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0, 0, 0, 0.1)",
        borderRadius: "0px 0px 0px 0px"
      }
    }
  ],
  product: [
    {
      id: "product1",
      name: "Product Grid",
      description: "Sản phẩm hiển thị dạng lưới",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjEwIiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMDAwIiBmb250LXdlaWdodD0iYm9sZCI+U+G6o24gcGjhuqltIG7hu5VpIGLhuq10PC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iMzAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iI0Y5RkFGQiIgc3Ryb2tlPSIjRTVFN0VCIiByeD0iOCIvPgo8cmVjdCB4PSIxMTAiIHk9IjMwIiB3aWR0aD0iODAiIGhlaWdodD0iODAiIGZpbGw9IiNGOUZBRkIiIHN0cm9rZT0iI0U1RTdFQiIgcng9IjgiLz4KPC9zdmc+",
      settings: {
        title: "Sản phẩm nổi bật",
        displayType: "grid",
        margin: "0px 0px 0px 0px",
        padding: "20px 8px 20px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px"
      }
    },
    {
      id: "product2",
      name: "Product Carousel",
      description: "Sản phẩm hiển thị dạng carousel",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDIwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjEwIiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMDAwIiBmb250LXdlaWdodD0iYm9sZCI+R+G7o2kgw70gY2hvIGLhuqFuPC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iMzAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI2MCIgZmlsbD0iI0Y5RkFGQiIgc3Ryb2tlPSIjRTVFN0VCIiByeD0iNCIvPgo8cmVjdCB4PSI3MCIgeT0iMzAiIHdpZHRoPSI1MCIgaGVpZ2h0PSI2MCIgZmlsbD0iI0Y5RkFGQiIgc3Ryb2tlPSIjRTVFN0VCIiByeD0iNCIvPgo8cmVjdCB4PSIxMzAiIHk9IjMwIiB3aWR0aD0iNTAiIGhlaWdodD0iNjAiIGZpbGw9IiNGOUZBRkIiIHN0cm9rZT0iI0U1RTdFQiIgcng9IjQiLz4KPHJlY3QgeD0iMTg1IiB5PSIzNSIgd2lkdGg9IjEwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjRjVGNUY1IiByeD0iMiIvPgo8L3N2Zz4=",
      settings: {
        title: "Gợi ý cho bạn",
        displayType: "carousel",
        margin: "0px 0px 0px 0px",
        padding: "20px 8px 20px 8px",
        background: "linear-gradient(180deg, rgb(252, 236, 193) 0%, rgb(252, 247, 233) 100%)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px"
      }
    }
  ],
  news: [
    {
      id: "news1",
      name: "News List",
      description: "Tin tức hiển thị dạng danh sách",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjEwIiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMDAwIiBmb250LXdlaWdodD0iYm9sZCI+VGluIHThu6ljPC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iMzAiIHdpZHRoPSIxODAiIGhlaWdodD0iMjAiIGZpbGw9IiNGOUZBRkIiIHN0cm9rZT0iI0U1RTdFQiIgcng9IjQiLz4KPHJlY3QgeD0iMTAiIHk9IjU1IiB3aWR0aD0iMTgwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjRjlGQUZCIiBzdHJva2U9IiNFNUU3RUIiIHJ4PSI0Ii8+CjxyZWN0IHg9IjEwIiB5PSI4MCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIyMCIgZmlsbD0iI0Y5RkFGQiIgc3Ryb2tlPSIjRTVFN0VCIiByeD0iNCIvPgo8L3N2Zz4=",
      settings: {
        title: "Tin tức",
        maxItems: 5,
        margin: "0px 0px 0px 0px",
        padding: "16px 8px 16px 8px",
        background: "rgba(255,255,255,1)",
        border: "0px solid rgba(0,0,0,0.1)",
        borderRadius: "0px 0px 0px 0px"
      }
    },
    {
      id: "news2",
      name: "News Card",
      description: "Tin tức hiển thị dạng card",
      preview: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDIwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjEwIiB5PSIyMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjMDAwIiBmb250LXdlaWdodD0iYm9sZCI+VGluIHThu6ljPC90ZXh0Pgo8cmVjdCB4PSIxMCIgeT0iMzAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iI0Y5RkFGQiIgc3Ryb2tlPSIjRTVFN0VCIiByeD0iOCIvPgo8cmVjdCB4PSIxMTAiIHk9IjMwIiB3aWR0aD0iODAiIGhlaWdodD0iODAiIGZpbGw9IiNGOUZBRkIiIHN0cm9rZT0iI0U1RTdFQiIgcng9IjgiLz4KPC9zdmc+",
      settings: {
        title: "Tin tức nổi bật",
        maxItems: 4,
        margin: "0px 0px 0px 0px",
        padding: "16px 8px 16px 8px",
        background: "rgba(255,255,255,1)",
        border: "1px solid rgba(229, 231, 235, 1)",
        borderRadius: "8px 8px 8px 8px",
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
      }
    }
  ]
};

export const getTemplatesForType = (type: string): TemplatePreview[] => {
  return TEMPLATE_LIBRARY[type] || [];
};

export const getTemplateById = (type: string, id: string): TemplatePreview | null => {
  const templates = getTemplatesForType(type);
  return templates.find(template => template.id === id) || null;
};
