import React from "react";
import { <PERSON>, Ch<PERSON>ronDown, ChevronUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSection: any;
  onUpdateSection: (sectionId: string, updates: any) => void;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  selectedSection,
  onUpdateSection,
}) => {
  const [openSections, setOpenSections] = React.useState<string[]>([
    "templates",
    "spacing",
    "style",
    "general",
  ]);

  if (!isOpen || !selectedSection) return null;

  const toggleSection = (section: string) => {
    setOpenSections((prev) =>
      prev.includes(section)
        ? prev.filter((s) => s !== section)
        : [...prev, section]
    );
  };

  const updateSetting = (path: string, value: any) => {
    const pathArray = path.split(".");
    const updates = { ...selectedSection };

    let current = updates;
    for (let i = 0; i < pathArray.length - 1; i++) {
      if (!current[pathArray[i]]) {
        current[pathArray[i]] = {};
      }
      current = current[pathArray[i]];
    }
    current[pathArray[pathArray.length - 1]] = value;

    onUpdateSection(selectedSection.id, updates);
  };

  const getSectionType = () => {
    const layoutKey = selectedSection.id;
    const templateMatch = layoutKey.match(/^([A-Za-z]+)(\d+)(_\d+)?$/);
    if (!templateMatch) return "unknown";
    return templateMatch[1].toLowerCase();
  };

  const sectionType = getSectionType();

  // Additional settings for different section types
  const renderCategorySettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Số cột</Label>
          <Select
            value={selectedSection.settings?.col?.toString() || "2"}
            onValueChange={(value) =>
              updateSetting("settings.col", parseInt(value))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4, 5, 6].map((num) => (
                <SelectItem key={num} value={num.toString()}>
                  {num} cột
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Kiểu card</Label>
          <Select
            value={selectedSection.settings?.cardStyle || "default"}
            onValueChange={(value) =>
              updateSetting("settings.cardStyle", value)
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="default">Mặc định</SelectItem>
              <SelectItem value="rounded">Bo góc</SelectItem>
              <SelectItem value="shadow">Có bóng</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderBannerSettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div className="flex items-center justify-between">
          <Label>Tự động chuyển</Label>
          <Switch
            checked={selectedSection.settings?.autoplay !== false}
            onCheckedChange={(checked) =>
              updateSetting("settings.autoplay", checked)
            }
          />
        </div>
        {selectedSection.settings?.autoplay !== false && (
          <div>
            <Label>Thời gian chuyển (ms)</Label>
            <Input
              type="number"
              value={selectedSection.settings?.autoplayDelay || 3000}
              onChange={(e) =>
                updateSetting(
                  "settings.autoplayDelay",
                  parseInt(e.target.value)
                )
              }
            />
          </div>
        )}
        <div className="flex items-center justify-between">
          <Label>Hiển thị chấm trang</Label>
          <Switch
            checked={selectedSection.settings?.animationPagination !== false}
            onCheckedChange={(checked) =>
              updateSetting("settings.animationPagination", checked)
            }
          />
        </div>
        <div>
          <Label>Chiều cao ảnh (px)</Label>
          <Slider
            value={[selectedSection.settings?.imageStyle?.height || 164]}
            onValueChange={([value]) =>
              updateSetting("settings.imageStyle.height", value)
            }
            max={300}
            min={100}
            step={10}
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderFlashSaleSettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Tiêu đề</Label>
          <Input
            value={selectedSection.settings?.title || ""}
            onChange={(e) => updateSetting("settings.title", e.target.value)}
            placeholder="Flash Sale"
          />
        </div>
        <div>
          <Label>Phụ đề</Label>
          <Input
            value={selectedSection.settings?.subTitle || ""}
            onChange={(e) => updateSetting("settings.subTitle", e.target.value)}
            placeholder="Kết thúc trong"
          />
        </div>
        <div className="flex items-center justify-between">
          <Label>Hiển thị đồng hồ đếm ngược</Label>
          <Switch
            checked={selectedSection.settings?.visibleTimer || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleTimer", checked)
            }
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderProductSettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Tiêu đề</Label>
          <Input
            value={selectedSection.settings?.title || ""}
            onChange={(e) => updateSetting("settings.title", e.target.value)}
            placeholder="Sản phẩm nổi bật"
          />
        </div>
        <div>
          <Label>Kiểu hiển thị</Label>
          <Select
            value={selectedSection.settings?.displayType || "grid"}
            onValueChange={(value) =>
              updateSetting("settings.displayType", value)
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="grid">Lưới</SelectItem>
              <SelectItem value="list">Danh sách</SelectItem>
              <SelectItem value="carousel">Carousel</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderNewsSettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Tiêu đề</Label>
          <Input
            value={selectedSection.settings?.title || ""}
            onChange={(e) => updateSetting("settings.title", e.target.value)}
            placeholder="Tin tức"
          />
        </div>
        <div>
          <Label>Số bài hiển thị</Label>
          <Slider
            value={[selectedSection.settings?.maxItems || 5]}
            onValueChange={([value]) =>
              updateSetting("settings.maxItems", value)
            }
            max={20}
            min={1}
            step={1}
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderDefaultSettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Tiêu đề</Label>
          <Input
            value={selectedSection.settings?.title || ""}
            onChange={(e) => updateSetting("settings.title", e.target.value)}
            placeholder="Nhập tiêu đề"
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderTemplateSettings = () => (
    <Collapsible open={openSections.includes("templates")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("templates")}
      >
        <span className="font-medium">Các mẫu {sectionType}</span>
        {openSections.includes("templates") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-3">
        <div className="grid grid-cols-2 gap-2">
          {[1, 2, 3, 4].map((template) => (
            <div
              key={template}
              className="p-2 border rounded cursor-pointer hover:bg-blue-50 hover:border-blue-300"
              onClick={() => updateSetting("templateVersion", template)}
            >
              <div className="w-full h-16 bg-gray-100 rounded mb-1"></div>
              <span className="text-xs">Mẫu {template}</span>
            </div>
          ))}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderSpacingSettings = () => (
    <Collapsible open={openSections.includes("spacing")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("spacing")}
      >
        <span className="font-medium">Khoảng cách</span>
        {openSections.includes("spacing") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Margin</Label>
          <Input
            value={selectedSection.settings?.margin || "0px"}
            onChange={(e) => updateSetting("settings.margin", e.target.value)}
            placeholder="0px"
          />
        </div>
        <div>
          <Label>Padding</Label>
          <Input
            value={selectedSection.settings?.padding || "16px"}
            onChange={(e) => updateSetting("settings.padding", e.target.value)}
            placeholder="16px"
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderStyleSettings = () => (
    <Collapsible open={openSections.includes("style")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("style")}
      >
        <span className="font-medium">Phong cách</span>
        {openSections.includes("style") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div>
          <Label>Nền</Label>
          <Input
            type="color"
            value={selectedSection.settings?.background || "#ffffff"}
            onChange={(e) =>
              updateSetting("settings.background", e.target.value)
            }
          />
        </div>
        <div>
          <Label>Bo góc</Label>
          <Input
            value={selectedSection.settings?.borderRadius || "0px"}
            onChange={(e) =>
              updateSetting("settings.borderRadius", e.target.value)
            }
            placeholder="0px"
          />
        </div>
        <div>
          <Label>Viền</Label>
          <Input
            value={selectedSection.settings?.border || "none"}
            onChange={(e) => updateSetting("settings.border", e.target.value)}
            placeholder="1px solid #e5e7eb"
          />
        </div>
        <div>
          <Label>Đổ bóng</Label>
          <Input
            value={selectedSection.settings?.boxShadow || ""}
            onChange={(e) =>
              updateSetting("settings.boxShadow", e.target.value)
            }
            placeholder="0 1px 3px rgba(0,0,0,0.1)"
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  const renderGeneralSettings = () => {
    switch (sectionType) {
      case "header":
        return renderHeaderSettings();
      case "category":
        return renderCategorySettings();
      case "banner":
        return renderBannerSettings();
      case "flashsale":
        return renderFlashSaleSettings();
      case "product":
        return renderProductSettings();
      case "news":
        return renderNewsSettings();
      default:
        return renderDefaultSettings();
    }
  };

  const renderHeaderSettings = () => (
    <Collapsible open={openSections.includes("general")}>
      <CollapsibleTrigger
        className="flex items-center justify-between w-full p-3 text-left hover:bg-gray-50"
        onClick={() => toggleSection("general")}
      >
        <span className="font-medium">Tổng quan</span>
        {openSections.includes("general") ? (
          <ChevronUp size={16} />
        ) : (
          <ChevronDown size={16} />
        )}
      </CollapsibleTrigger>
      <CollapsibleContent className="p-3 space-y-4">
        <div className="flex items-center justify-between">
          <Label>Hiển thị tiêu đề</Label>
          <Switch
            checked={selectedSection.settings?.visibleTitle || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleTitle", checked)
            }
          />
        </div>
        {selectedSection.settings?.visibleTitle && (
          <>
            <div>
              <Label>Tiêu đề</Label>
              <Input
                value={selectedSection.settings?.title || ""}
                onChange={(e) =>
                  updateSetting("settings.title", e.target.value)
                }
                placeholder="Nhập tiêu đề"
              />
            </div>
            <div>
              <Label>Kích thước chữ</Label>
              <Slider
                value={[selectedSection.settings?.fontSizeTitle || 16]}
                onValueChange={([value]) =>
                  updateSetting("settings.fontSizeTitle", value)
                }
                max={32}
                min={10}
                step={1}
              />
            </div>
            <div>
              <Label>Màu sắc</Label>
              <Input
                type="color"
                value={selectedSection.settings?.colorTitle || "#000000"}
                onChange={(e) =>
                  updateSetting("settings.colorTitle", e.target.value)
                }
              />
            </div>
          </>
        )}

        <div className="flex items-center justify-between">
          <Label>Hiển thị phụ đề</Label>
          <Switch
            checked={selectedSection.settings?.visibleSubTitle || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleSubTitle", checked)
            }
          />
        </div>
        {selectedSection.settings?.visibleSubTitle && (
          <>
            <div>
              <Label>Phụ đề</Label>
              <Input
                value={selectedSection.settings?.subTitle || ""}
                onChange={(e) =>
                  updateSetting("settings.subTitle", e.target.value)
                }
                placeholder="Nhập phụ đề"
              />
            </div>
            <div>
              <Label>Kích thước chữ</Label>
              <Slider
                value={[selectedSection.settings?.fontSizeSubTitle || 12]}
                onValueChange={([value]) =>
                  updateSetting("settings.fontSizeSubTitle", value)
                }
                max={24}
                min={8}
                step={1}
              />
            </div>
            <div>
              <Label>Màu sắc</Label>
              <Input
                type="color"
                value={selectedSection.settings?.colorSubTitle || "#666666"}
                onChange={(e) =>
                  updateSetting("settings.colorSubTitle", e.target.value)
                }
              />
            </div>
          </>
        )}

        <div className="flex items-center justify-between">
          <Label>Hiển thị logo</Label>
          <Switch
            checked={selectedSection.settings?.visibleLogo || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleLogo", checked)
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <Label>Hiển thị icon giỏ hàng</Label>
          <Switch
            checked={selectedSection.settings?.visibleCartIcon || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleCartIcon", checked)
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <Label>Hiển thị icon tin nhắn</Label>
          <Switch
            checked={selectedSection.settings?.visibleMessageIcon || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleMessageIcon", checked)
            }
          />
        </div>

        <div className="flex items-center justify-between">
          <Label>Hiển thị icon tìm kiếm</Label>
          <Switch
            checked={selectedSection.settings?.visibleSearchBar || false}
            onCheckedChange={(checked) =>
              updateSetting("settings.visibleSearchBar", checked)
            }
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  );

  return (
    <div className="fixed right-0 top-0 bottom-0 w-80 bg-white shadow-lg z-50 overflow-y-auto">
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="font-semibold">Cài đặt phần tử</h3>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X size={16} />
        </Button>
      </div>

      <div className="divide-y">
        {renderTemplateSettings()}
        {renderSpacingSettings()}
        {renderStyleSettings()}
        {renderGeneralSettings()}
      </div>
    </div>
  );
};
